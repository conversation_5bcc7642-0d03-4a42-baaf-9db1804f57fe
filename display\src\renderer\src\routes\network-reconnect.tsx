import { createFileRoute, redirect, useNavigate } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import authService from "../services/auth.service";
// Removed EncryptedAssets import - using normal HTML tags for production compatibility

export const Route = createFileRoute("/network-reconnect")({
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();

  const { data, isError, isLoading, isSuccess, error } = useQuery({
    queryKey: ["server-health"],
    queryFn: async () => {
      try {
        const response = await authService.getAuthInfo();
        console.log("response", response);
        return response;
      } catch (err: any) {
        if (err.response && err.response.status) {
          console.log("response", err.response);
          if (err.response && err.response.status === 401)
            navigate({ to: "/auth/login", replace: true });
          throw { ...err, statusCode: err.response.status };
        }
        throw err;
      }
    },
    retry: true,
    retryDelay: 10000,
    refetchInterval: 10000,
    refetchIntervalInBackground: true,
  });

  useEffect(() => {
    if (isError && (error as any)?.statusCode === 401) {
      navigate({ to: "/auth/login", replace: true });
    } else if (data && !isError) {
      navigate({ to: "/", replace: true });
    }
  }, [data, isError, error, navigate, isLoading]);

  return (
    <div className="relative h-screen">
      <img
        src="images/bg.png"
        crossOrigin="anonymous"
        alt="images/bg.png"
        className="absolute inset-0 h-full w-full object-cover"
      />
      <div className="absolute top-4 left-4 text-white uppercase">
        <p>Connecting...</p>
      </div>

      <div className="absolute inset-0 grid h-full place-items-center text-center">
        <div className="space-y-4 uppercase">
          <p className="text-[4vw] font-medium text-white">
            More Events Shortly...
          </p>
          <p className="text-[2.8vw] font-medium text-white">
            Events happen daily from 7:00 Until 22:00
          </p>
        </div>
      </div>

      <div className="absolute right-4 bottom-4 text-white">
        <p>18.131.15.66</p>
      </div>
    </div>
  );
}
