/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M13 17V9", key: "1fwyjl" }],
  ["path", { d: "M18 17v-3", key: "1sqioe" }],
  ["path", { d: "M3 3v16a2 2 0 0 0 2 2h16", key: "c24i48" }],
  ["path", { d: "M8 17V5", key: "1wzmnc" }]
];
const ChartColumnDecreasing = createLucideIcon("chart-column-decreasing", __iconNode);

export { __iconNode, ChartColumnDecreasing as default };
//# sourceMappingURL=chart-column-decreasing.js.map
