/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["path", { d: "m10 8 4 4-4 4", key: "1wy4r4" }]
];
const CircleChevronRight = createLucideIcon("circle-chevron-right", __iconNode);

export { __iconNode, CircleChevronRight as default };
//# sourceMappingURL=circle-chevron-right.js.map
