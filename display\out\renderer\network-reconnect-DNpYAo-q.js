import { u as useNavigate, r as reactExports, j as jsxRuntimeExports, a as authService } from "./index-DTOJzlLF.js";
import { u as useQuery } from "./useQuery-D0vAnUpp.js";
import "./utils-QRJGL1kX.js";
const SplitComponent = function RouteComponent() {
  const navigate = useNavigate();
  const {
    data,
    isError,
    isLoading,
    isSuccess,
    error
  } = useQuery({
    queryKey: ["server-health"],
    queryFn: async () => {
      try {
        const response = await authService.getAuthInfo();
        console.log("response", response);
        return response;
      } catch (err) {
        if (err.response && err.response.status) {
          console.log("response", err.response);
          if (err.response && err.response.status === 401) navigate({
            to: "/auth/login",
            replace: true
          });
          throw {
            ...err,
            statusCode: err.response.status
          };
        }
        throw err;
      }
    },
    retry: true,
    retryDelay: 1e4,
    refetchInterval: 1e4,
    refetchIntervalInBackground: true
  });
  reactExports.useEffect(() => {
    if (isError && error?.statusCode === 401) {
      navigate({
        to: "/auth/login",
        replace: true
      });
    } else if (data && !isError) {
      navigate({
        to: "/",
        replace: true
      });
    }
  }, [data, isError, error, navigate, isLoading]);
  return /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "relative h-screen", children: [
    /* @__PURE__ */ jsxRuntimeExports.jsx("img", { src: "images/bg.png", crossOrigin: "anonymous", alt: "images/bg.png", className: "absolute inset-0 h-full w-full object-cover" }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute top-4 left-4 text-white uppercase", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "Connecting..." }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute inset-0 grid h-full place-items-center text-center", children: /* @__PURE__ */ jsxRuntimeExports.jsxs("div", { className: "space-y-4 uppercase", children: [
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-[4vw] font-medium text-white", children: "More Events Shortly..." }),
      /* @__PURE__ */ jsxRuntimeExports.jsx("p", { className: "text-[2.8vw] font-medium text-white", children: "Events happen daily from 7:00 Until 22:00" })
    ] }) }),
    /* @__PURE__ */ jsxRuntimeExports.jsx("div", { className: "absolute right-4 bottom-4 text-white", children: /* @__PURE__ */ jsxRuntimeExports.jsx("p", { children: "18.131.15.66" }) })
  ] });
};
export {
  SplitComponent as component
};
