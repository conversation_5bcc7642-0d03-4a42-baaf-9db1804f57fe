// Removed EncryptedAssets import - using normal HTML tags for production compatibility

export const PageLoader = () => {
  return (
    <div className="relative h-screen">
      <img
        src="images/bg.png"
        crossOrigin="anonymous"
        alt="images/bg.png"
        className="absolute inset-0 h-full w-full object-cover"
      />
      <div className="absolute top-4 left-4 text-white uppercase">
        <p>Connecting...</p>
      </div>

      <div className="absolute inset-0 grid h-full place-items-center text-center">
        <div className="space-y-4 uppercase">
          <p className="text-[4vw] font-medium text-white">
            More Events Shortly...
          </p>
          <p className="text-[2.8vw] font-medium text-white">
            Events happen daily from 7:00 Until 22:00
          </p>
        </div>
      </div>

      <div className="absolute right-4 bottom-4 text-white">
        <p>18.131.15.66</p>
      </div>
    </div>
  );
};
