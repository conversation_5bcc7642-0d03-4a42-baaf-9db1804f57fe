/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M22 2 2 22", key: "y4kqgn" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const CircleSlash2 = createLucideIcon("circle-slash-2", __iconNode);

export { __iconNode, CircleSlash2 as default };
//# sourceMappingURL=circle-slash-2.js.map
