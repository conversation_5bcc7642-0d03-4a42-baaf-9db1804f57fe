/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 20v2", key: "1n8e1g" }],
  ["path", { d: "M14 20v2", key: "1lq872" }],
  ["path", { d: "M18 20v2", key: "10uadw" }],
  ["path", { d: "M21 20H3", key: "kdqkdp" }],
  ["path", { d: "M6 20v2", key: "a9bc87" }],
  ["path", { d: "M8 16V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v12", key: "17n9tx" }],
  ["rect", { x: "4", y: "6", width: "16", height: "10", rx: "2", key: "1097i5" }]
];
const BriefcaseConveyorBelt = createLucideIcon("briefcase-conveyor-belt", __iconNode);

export { __iconNode, BriefcaseConveyorBelt as default };
//# sourceMappingURL=briefcase-conveyor-belt.js.map
